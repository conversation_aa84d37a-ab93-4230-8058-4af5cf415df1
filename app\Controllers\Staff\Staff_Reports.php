<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Helpers\DummyDataHelper;

class Staff_Reports extends BaseController
{
    public function __construct()
    {
        helper(['url', 'form', 'info']);
    }

    public function farmers()
    {
        $farmers = $this->getFarmersWithDetails();

        // Calculate summary statistics
        $totalCropBlocks = array_sum(array_column($farmers, 'crop_blocks_count'));
        $totalLivestockBlocks = array_sum(array_column($farmers, 'livestock_blocks_count'));
        $farmersWithEmail = count(array_filter($farmers, fn($f) => !empty($f['email'])));
        $farmersWithPhone = count(array_filter($farmers, fn($f) => !empty($f['phone'])));

        // Get crop distribution data
        $crop_distribution = $this->getCropDistributionByLLG();

        $data = [
            'title' => 'Farmers Reports',
            'page_header' => 'Farmers Reports',
            'farmers' => $farmers,
            'crop_distribution' => $crop_distribution,
            'stats' => [
                'total_farmers' => count($farmers),
                'total_crop_blocks' => $totalCropBlocks,
                'total_livestock_blocks' => $totalLivestockBlocks,
                'farmers_with_email' => $farmersWithEmail,
                'farmers_with_phone' => $farmersWithPhone,
                'active_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'active')),
                'inactive_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'inactive'))
            ]
        ];

        return view('staff/reports/farmers', $data);
    }

    private function getFarmersWithDetails()
    {
        // Get dummy farmers data
        $farmers = DummyDataHelper::getDummyFarmers();
        $llgs = DummyDataHelper::getDummyLLGs();
        $wards = DummyDataHelper::getDummyWards();

        // Enhance farmers data with additional details
        foreach ($farmers as &$farmer) {
            // Add LLG name
            $llg = array_filter($llgs, fn($l) => $l['id'] == ($farmer['llg_id'] ?? 1));
            $farmer['llg_name'] = !empty($llg) ? array_values($llg)[0]['name'] : 'Moresby North-East';

            // Add Ward name
            $ward = array_filter($wards, fn($w) => $w['id'] == ($farmer['ward_id'] ?? 1));
            $farmer['ward_name'] = !empty($ward) ? array_values($ward)[0]['name'] : 'Gerehu Ward 1';

            // Add dummy counts
            $farmer['children_count'] = rand(0, 5);
            $farmer['crop_blocks_count'] = rand(1, 3);
            $farmer['farm_blocks_count'] = $farmer['crop_blocks_count']; // For view compatibility
            $farmer['livestock_blocks_count'] = rand(0, 2);

            // Add date_of_birth for age calculation
            $farmer['date_of_birth'] = date('Y-m-d', strtotime('-' . $farmer['age'] . ' years'));
        }

        return $farmers;
    }

    private function getCropDistributionByLLG()
    {
        // Return dummy crop distribution data
        $llgs = DummyDataHelper::getDummyLLGs();
        $crops = DummyDataHelper::getDummyCrops();

        $distribution = [];
        $cropTypes = [];

        foreach ($llgs as $llg) {
            foreach ($crops as $crop) {
                $farmerCount = rand(5, 25);
                $distribution[$llg['name']][$crop['name']] = $farmerCount;
                if (!in_array($crop['name'], $cropTypes)) {
                    $cropTypes[] = $crop['name'];
                }
            }
        }

        return [
            'distribution' => $distribution,
            'crop_types' => $cropTypes
        ];
    }

    public function crops()
    {
        // Get dummy crops data
        $crops = DummyDataHelper::getDummyCrops();
        $crops_data = [];

        // Generate dummy crop data
        foreach ($crops as $crop) {
            $crops_data[] = [
                'id' => $crop['id'],
                'crop_name' => $crop['name'],
                'category' => $crop['category'],
                'farmer_count' => rand(10, 50),
                'total_blocks' => rand(15, 75),
                'total_hectares' => rand(100, 500),
                'harvest_records' => rand(5, 25)
            ];
        }

        $data = [
            'title' => 'Crops Reports',
            'page_header' => 'Crops Reports',
            'crops_data' => $crops_data,
            'crops' => $crops
        ];

        return view('staff_reports/staff_reports_crops', $data);
    }

    public function blocks()
    {
        // Get dummy farm blocks data
        $blocks_data = DummyDataHelper::getDummyFarmBlocks();
        $llgs = DummyDataHelper::getDummyLLGs();
        $wards = DummyDataHelper::getDummyWards();

        // Enhance blocks data with additional details
        foreach ($blocks_data as &$block) {
            // Add LLG name
            $llg = array_filter($llgs, fn($l) => $l['id'] == $block['llg_id']);
            $block['llg_name'] = !empty($llg) ? array_values($llg)[0]['name'] : 'Moresby North-East';

            // Add Ward name
            $ward = array_filter($wards, fn($w) => $w['id'] == $block['ward_id']);
            $block['ward_name'] = !empty($ward) ? array_values($ward)[0]['name'] : 'Gerehu Ward 1';

            // Add crop color code
            $block['crop_color_code'] = '#' . substr(md5($block['crop_name']), 0, 6);
        }

        // Get statistics for blocks
        $stats = [
            'total_blocks' => count($blocks_data),
            'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
            'by_llg' => [],
            'by_ward' => [],
            'by_crop' => []
        ];

        // Process statistics and crop colors
        $cropColors = [];
        foreach ($blocks_data as $block) {
            // Count by crop
            if (!isset($stats['by_crop'][$block['crop_name']])) {
                $stats['by_crop'][$block['crop_name']] = 0;
                $cropColors[$block['crop_name']] = $block['crop_color_code'];
            }
            $stats['by_crop'][$block['crop_name']]++;

            // Count by LLG
            if (!isset($stats['by_llg'][$block['llg_name']])) {
                $stats['by_llg'][$block['llg_name']] = 0;
            }
            $stats['by_llg'][$block['llg_name']]++;

            // Count by Ward
            if (!isset($stats['by_ward'][$block['ward_name']])) {
                $stats['by_ward'][$block['ward_name']] = 0;
            }
            $stats['by_ward'][$block['ward_name']]++;
        }

        $data = [
            'title' => 'Farm Blocks Reports',
            'page_header' => 'Farm Blocks Reports',
            'blocks_data' => $blocks_data,
            'stats' => $stats,
            'cropColors' => $cropColors
        ];

        return view('staff_reports/staff_reports_blocks', $data);
    }

    public function diseases()
    {
        // Generate dummy diseases data
        $diseases_data = [
            [
                'id' => 1,
                'disease_name' => 'Sweet Potato Weevil',
                'crop_name' => 'Sweet Potato',
                'crop_color_code' => '#FF6B35',
                'farmer_name' => 'Peter Kaupa',
                'block_code' => 'BLK001',
                'number_of_plants' => 150,
                'hectares' => 2.5,
                'action_date' => date('Y-m-d', strtotime('-30 days')),
                'district_name' => 'Port Moresby',
                'block_status' => 'active'
            ],
            [
                'id' => 2,
                'disease_name' => 'Banana Bunchy Top',
                'crop_name' => 'Banana',
                'crop_color_code' => '#FFD23F',
                'farmer_name' => 'Maria Temu',
                'block_code' => 'BLK002',
                'number_of_plants' => 75,
                'hectares' => 1.8,
                'action_date' => date('Y-m-d', strtotime('-15 days')),
                'district_name' => 'Port Moresby',
                'block_status' => 'active'
            ],
            [
                'id' => 3,
                'disease_name' => 'Cassava Mosaic',
                'crop_name' => 'Cassava',
                'crop_color_code' => '#8B5A3C',
                'farmer_name' => 'James Wambi',
                'block_code' => 'BLK003',
                'number_of_plants' => 200,
                'hectares' => 3.2,
                'action_date' => date('Y-m-d', strtotime('-7 days')),
                'district_name' => 'Port Moresby',
                'block_status' => 'active'
            ]
        ];

        // Process statistics
        $stats = [
            'total_cases' => count($diseases_data),
            'total_plants_affected' => array_sum(array_column($diseases_data, 'number_of_plants')),
            'total_hectares_affected' => array_sum(array_column($diseases_data, 'hectares')),
            'by_disease' => [],
            'by_crop' => [],
            'by_district' => [],
            'monthly_cases' => array_fill(0, 12, 0)
        ];

        foreach ($diseases_data as $case) {
            // Count by disease type
            if (!isset($stats['by_disease'][$case['disease_name']])) {
                $stats['by_disease'][$case['disease_name']] = [
                    'count' => 0,
                    'plants' => 0,
                    'hectares' => 0
                ];
            }
            $stats['by_disease'][$case['disease_name']]['count']++;
            $stats['by_disease'][$case['disease_name']]['plants'] += $case['number_of_plants'];
            $stats['by_disease'][$case['disease_name']]['hectares'] += $case['hectares'];

            // Count by crop
            if (!isset($stats['by_crop'][$case['crop_name']])) {
                $stats['by_crop'][$case['crop_name']] = [
                    'count' => 0,
                    'color' => $case['crop_color_code']
                ];
            }
            $stats['by_crop'][$case['crop_name']]['count']++;

            // Count by district
            if (!isset($stats['by_district'][$case['district_name']])) {
                $stats['by_district'][$case['district_name']] = 0;
            }
            $stats['by_district'][$case['district_name']]++;

            // Count monthly cases
            $month = date('n', strtotime($case['action_date'])) - 1;
            $stats['monthly_cases'][$month]++;
        }

        $data = [
            'title' => 'Diseases Reports',
            'page_header' => 'Diseases Reports',
            'diseases_data' => $diseases_data,
            'stats' => $stats
        ];

        return view('staff_reports/staff_reports_diseases', $data);
    }

    public function fertilizer()
    {
        // Get fertilizer data with active blocks only
        $fertilizer_data = $this->farmFertilizerDataModel->getFertilizerReportData();

        // Filter for active blocks only
        $fertilizer_data = array_filter($fertilizer_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $fertilizer_data = array_values($fertilizer_data);

        $data = [
            'title' => 'Fertilizer Reports',
            'page_header' => 'Fertilizer Reports',
            'fertilizer_data' => $fertilizer_data,
            'fertilizers' => $this->fertilizersModel->findAll()
        ];

        return view('staff_reports/staff_reports_fertilizer', $data);
    }

    public function pesticides()
    {
        // Get pesticides data with active blocks only
        $pesticides_data = $this->farmPesticidesDataModel->getPesticidesReportData();

        // Filter for active blocks only
        $pesticides_data = array_filter($pesticides_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $pesticides_data = array_values($pesticides_data);

        $data = [
            'title' => 'Pesticides Reports',
            'page_header' => 'Pesticides Reports',
            'pesticides_data' => $pesticides_data,
            'pesticides' => $this->pesticidesModel->findAll()
        ];

        return view('staff_reports/staff_reports_pesticides', $data);
    }

    public function harvests()
    {
        // Get harvest data with active blocks only
        $harvest_data = $this->farmHarvestDataModel->getHarvestReportData();

        // Filter for active blocks only
        $harvest_data = array_filter($harvest_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $harvest_data = array_values($harvest_data);

        // Process statistics
        $stats = [
            'total_harvests' => count($harvest_data),
            'total_quantity' => array_sum(array_column($harvest_data, 'quantity')),
            'by_crop' => [],
            'by_district' => [],
            'monthly_harvests' => array_fill(0, 12, 0),
            'monthly_quantity' => array_fill(0, 12, 0)
        ];

        foreach ($harvest_data as $harvest) {
            // Stats by crop
            if (!isset($stats['by_crop'][$harvest['crop_name']])) {
                $stats['by_crop'][$harvest['crop_name']] = [
                    'quantity' => 0,
                    'harvests' => 0,
                    'color' => $harvest['crop_color_code']
                ];
            }
            $stats['by_crop'][$harvest['crop_name']]['quantity'] += $harvest['quantity'];
            $stats['by_crop'][$harvest['crop_name']]['harvests']++;

            // Stats by district
            if (!isset($stats['by_district'][$harvest['district_name']])) {
                $stats['by_district'][$harvest['district_name']] = [
                    'quantity' => 0,
                    'harvests' => 0
                ];
            }
            $stats['by_district'][$harvest['district_name']]['quantity'] += $harvest['quantity'];
            $stats['by_district'][$harvest['district_name']]['harvests']++;

            // Monthly stats
            $month = date('n', strtotime($harvest['harvest_date'])) - 1;
            $stats['monthly_harvests'][$month]++;
            $stats['monthly_quantity'][$month] += $harvest['quantity'];
        }

        // Sort arrays by quantity
        arsort($stats['by_crop']);
        arsort($stats['by_district']);

        $data = [
            'title' => 'Harvest Reports',
            'page_header' => 'Harvest Reports',
            'harvest_data' => $harvest_data,
            'stats' => $stats
        ];

        return view('staff_reports/staff_reports_harvests', $data);
    }

    public function marketing()
    {
        // Get marketing data
        $conditions = [];

        // Only filter by district if it's set in the session
        if (session()->has('district_id') && session()->get('district_id') > 0) {
            $conditions['crops_farm_marketing_data.district_id'] = session()->get('district_id');
        }

        $marketing_data = $this->farmMarketingDataModel->getMarketingReportData($conditions);

        // Log the number of records found
        log_message('debug', 'Marketing Report: Found ' . count($marketing_data) . ' records');

        // Process statistics
        $stats = [
            'total_transactions' => count($marketing_data),
            'total_revenue' => 0,
            'total_quantity' => 0,
            'by_crop' => [],
            'by_buyer' => [],
            'monthly_revenue' => array_fill(0, 12, 0)
        ];

        // Group data by farmer
        $farmer_totals = [];
        foreach ($marketing_data as $transaction) {
            $farmer_name = $transaction['given_name'] . ' ' . $transaction['surname'];
            $quantity = floatval($transaction['quantity']);
            $revenue = $quantity * floatval($transaction['market_price_per_unit']);
            $freight_cost = floatval($transaction['total_freight_cost'] ?? 0);

            if (!isset($farmer_totals[$farmer_name])) {
                $farmer_totals[$farmer_name] = [
                    'farmer_name' => $farmer_name,
                    'total_revenue' => 0,
                    'total_freight_cost' => 0,
                    'total_quantity' => 0,
                    'llg_name' => $transaction['llg_name'],
                    'crops' => []
                ];
            }

            // Update farmer totals
            $farmer_totals[$farmer_name]['total_revenue'] += $revenue;
            $farmer_totals[$farmer_name]['total_freight_cost'] += $freight_cost;
            $farmer_totals[$farmer_name]['total_quantity'] += $quantity;

            // Track crops for this farmer
            if (!in_array($transaction['crop_name'], $farmer_totals[$farmer_name]['crops'])) {
                $farmer_totals[$farmer_name]['crops'][] = $transaction['crop_name'];
            }

            // Update overall stats
            $stats['total_revenue'] += $revenue;
            $stats['total_quantity'] += floatval($transaction['quantity']);

            // Stats by crop
            if (!isset($stats['by_crop'][$transaction['crop_name']])) {
                $stats['by_crop'][$transaction['crop_name']] = [
                    'revenue' => 0,
                    'color' => $transaction['crop_color_code']
                ];
            }
            $stats['by_crop'][$transaction['crop_name']]['revenue'] += $revenue;

            // Stats by buyer
            $buyer_name = $transaction['buyer_name'] ?? 'Unknown';
            if (!isset($stats['by_buyer'][$buyer_name])) {
                $stats['by_buyer'][$buyer_name] = [
                    'revenue' => 0
                ];
            }
            $stats['by_buyer'][$buyer_name]['revenue'] += $revenue;

            // Monthly stats
            $month = date('n', strtotime($transaction['market_date'])) - 1;
            $stats['monthly_revenue'][$month] += $revenue;
        }

        // Sort farmers by total revenue
        uasort($farmer_totals, function($a, $b) {
            return $b['total_revenue'] <=> $a['total_revenue'];
        });

        $data = [
            'title' => 'Marketing Reports',
            'page_header' => 'Marketing Reports',
            'farmer_totals' => $farmer_totals,
            'stats' => $stats
        ];

        return view('staff_reports/staff_reports_marketing', $data);
    }

    public function livestock_blocks()
    {
        try {
            // Get livestock colors from the model property
            $livestock_colors = [];
            foreach ($this->livestockModel->findAll() as $livestock) {
                $livestock_colors[$livestock['name']] = $livestock['color_code'];
            }

            // Fetch livestock farm blocks data with necessary joins including livestock types
            $blocks_data = $this->livestockFarmBlockModel
                ->select('livestock_farm_blocks.*,
                          farmer_information.given_name,
                          farmer_information.surname,
                          ward.name as ward_name,
                          llg.name as llg_name,
                          district.name as district_name,
                          province.name as province_name,
                          GROUP_CONCAT(DISTINCT adx_livestock.name) as livestock_types,
                          GROUP_CONCAT(DISTINCT adx_livestock.color_code) as livestock_colors')
                ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
                ->join('adx_ward ward', 'ward.id = livestock_farm_blocks.ward_id', 'left')
                ->join('adx_llg llg', 'llg.id = livestock_farm_blocks.llg_id', 'left')
                ->join('adx_district district', 'district.id = livestock_farm_blocks.district_id', 'left')
                ->join('adx_province province', 'province.id = livestock_farm_blocks.province_id', 'left')
                ->join('livestock_farm_data', 'livestock_farm_data.block_id = livestock_farm_blocks.id', 'left')
                ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id', 'left')
                ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
                ->where('livestock_farm_blocks.status !=', 'deleted')
                ->groupBy('livestock_farm_blocks.id')
                ->orderBy('livestock_farm_blocks.id', 'ASC')
                ->findAll();

            if (empty($blocks_data)) {
                log_message('warning', '[Livestock Blocks Report] No livestock farm blocks found');
                $data = [
                    'title' => 'Livestock Farm Blocks Reports',
                    'page_header' => 'Livestock Farm Blocks Reports',
                    'blocks_data' => [],
                    'stats' => [
                        'total_blocks' => 0,
                        'total_active' => 0,
                        'total_inactive' => 0,
                        'by_district' => [],
                        'by_llg' => [],
                        'by_ward' => [],
                        'by_livestock_type' => [],
                        'livestock_colors' => []
                    ]
                ];
                return view('staff_reports/staff_reports_livestock_blocks', $data);
            }

            // Process statistics
            $stats = [
                'total_blocks' => count($blocks_data),
                'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
                'total_inactive' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'inactive')),
                'by_llg' => [],
                'by_livestock_type' => [],
                'livestock_colors' => $livestock_colors
            ];

            foreach ($blocks_data as $block) {
                // Process LLG stats
                if (!empty($block['llg_name'])) {
                    if (!isset($stats['by_llg'][$block['llg_name']])) {
                        $stats['by_llg'][$block['llg_name']] = 0;
                    }
                    $stats['by_llg'][$block['llg_name']]++;
                }

                // Process livestock type stats
                if (!empty($block['livestock_types'])) {
                    $types = explode(',', $block['livestock_types']);
                    foreach ($types as $type) {
                        if (!isset($stats['by_livestock_type'][$type])) {
                            $stats['by_livestock_type'][$type] = 0;
                        }
                        $stats['by_livestock_type'][$type]++;
                    }
                }
            }

            $data = [
                'title' => 'Livestock Farm Blocks Reports',
                'page_header' => 'Livestock Farm Blocks Reports',
                'blocks_data' => $blocks_data,
                'stats' => $stats
            ];

            return view('staff_reports/staff_reports_livestock_blocks', $data);

        } catch (\Exception $e) {
            log_message('error', '[Livestock Blocks Report] ' . $e->getMessage());
            $data = [
                'title' => 'Livestock Farm Blocks Reports',
                'page_header' => 'Livestock Farm Blocks Reports',
                'blocks_data' => [],
                'stats' => [
                    'total_blocks' => 0,
                    'total_active' => 0,
                    'total_inactive' => 0,
                    'by_district' => [],
                    'by_llg' => [],
                    'by_ward' => [],
                    'by_livestock_type' => [],
                    'livestock_colors' => []
                ],
                'error' => 'Failed to retrieve livestock blocks report'
            ];
            return view('staff_reports/staff_reports_livestock_blocks', $data);
        }
    }

    public function livestock_data()
    {
        // Get livestock farm data with details
        $livestock_data = $this->livestockFarmDataModel
            ->select('livestock_farm_data.*, livestock_farm_blocks.block_code, livestock_farm_blocks.status as block_status,
                    farmer_information.given_name, farmer_information.surname,
                    adx_livestock.name as livestock_name,
                    adx_llg.name as llg_name')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->where('livestock_farm_data.status !=', 'deleted')
            ->findAll();

        // Process statistics
        $stats = [
            'total_records' => count($livestock_data),
            'total_male' => array_sum(array_column($livestock_data, 'he_total')),
            'total_female' => array_sum(array_column($livestock_data, 'she_total')),
            'by_llg' => [],
            'monthly_data' => array_fill(0, 12, ['count' => 0, 'total' => 0])
        ];

        foreach ($livestock_data as $record) {
            $total = $record['he_total'] + $record['she_total'];

            // Stats by LLG
            if (!isset($stats['by_llg'][$record['llg_name']])) {
                $stats['by_llg'][$record['llg_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0,
                    'by_livestock_type' => []
                ];
            }

            // Initialize livestock type stats for this LLG if not exists
            if (!isset($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']])) {
                $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0
                ];
            }

            // Cost statistics
            $cost = floatval($record['cost_per_livestock']);
            $low_price = floatval($record['low_price_per_livestock']);
            $high_price = floatval($record['high_price_per_livestock']);

            // Update LLG totals
            $stats['by_llg'][$record['llg_name']]['total'] += $total;
            $stats['by_llg'][$record['llg_name']]['count']++;
            $stats['by_llg'][$record['llg_name']]['total_cost'] += $cost;
            $stats['by_llg'][$record['llg_name']]['total_low_price'] += $low_price;
            $stats['by_llg'][$record['llg_name']]['total_high_price'] += $high_price;

            // Update min/max values for LLG
            $stats['by_llg'][$record['llg_name']]['min_cost'] = min($stats['by_llg'][$record['llg_name']]['min_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['max_cost'] = max($stats['by_llg'][$record['llg_name']]['max_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['min_low_price'] = min($stats['by_llg'][$record['llg_name']]['min_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['max_low_price'] = max($stats['by_llg'][$record['llg_name']]['max_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['min_high_price'] = min($stats['by_llg'][$record['llg_name']]['min_high_price'], $high_price);
            $stats['by_llg'][$record['llg_name']]['max_high_price'] = max($stats['by_llg'][$record['llg_name']]['max_high_price'], $high_price);

            // Update livestock type stats for this LLG
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total'] += $total;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['count']++;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_cost'] += $cost;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_low_price'] += $low_price;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_high_price'] += $high_price;

            // Update min/max values for livestock type in this LLG
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'], $high_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'], $high_price);
        }

        $data = [
            'title' => 'Livestock Farm Data Reports',
            'page_header' => 'Livestock Farm Data Reports',
            'livestock_data' => $livestock_data,
            'stats' => $stats
        ];

        return view('staff_reports/staff_reports_livestock_data', $data);
    }

    public function farmer_profile($farmer_id = null)
    {
        if (!$farmer_id) {
            return redirect()->to('/staff/reports/farmers');
        }

        // Get dummy farmer data
        $farmers = DummyDataHelper::getDummyFarmers();
        $farmer = null;

        foreach ($farmers as $f) {
            if ($f['id'] == $farmer_id) {
                $farmer = $f;
                break;
            }
        }

        if (!$farmer) {
            return redirect()->to('/staff/reports/farmers');
        }

        // Add additional location details
        $farmer['llg_name'] = 'Moresby North-East';
        $farmer['ward_name'] = 'Gerehu Ward 1';
        $farmer['district_name'] = 'Port Moresby';
        $farmer['province_name'] = 'National Capital District';
        $farmer['children_count'] = rand(0, 5);

        // Generate dummy children data
        $children = [];
        for ($i = 1; $i <= $farmer['children_count']; $i++) {
            $children[] = [
                'id' => $i,
                'name' => 'Child ' . $i,
                'age' => rand(5, 18),
                'gender' => rand(0, 1) ? 'Male' : 'Female',
                'in_school' => rand(0, 1) ? 'Yes' : 'No'
            ];
        }

        // Generate dummy farm blocks
        $farmBlocks = [];
        $crops = DummyDataHelper::getDummyCrops();
        for ($i = 1; $i <= rand(1, 3); $i++) {
            $crop = $crops[array_rand($crops)];
            $farmBlocks[] = [
                'id' => $i,
                'block_code' => 'BLK00' . $i,
                'crop_name' => $crop['name'],
                'crop_color_code' => '#' . substr(md5($crop['name']), 0, 6),
                'total_hectares' => rand(1, 5),
                'status' => 'active',
                'village' => ['Gerehu', '9-Mile', 'Saraga'][array_rand(['Gerehu', '9-Mile', 'Saraga'])],
                'block_site' => 'Site ' . $i,
                'created_at' => date('Y-m-d', strtotime('-' . rand(30, 365) . ' days'))
            ];
        }

        // Generate dummy livestock blocks
        $livestockBlocks = [];
        for ($i = 1; $i <= rand(0, 2); $i++) {
            $livestockBlocks[] = [
                'id' => $i,
                'block_code' => 'LBK00' . $i,
                'livestock_type' => ['Pigs', 'Chickens', 'Goats'][array_rand(['Pigs', 'Chickens', 'Goats'])],
                'number_of_animals' => rand(5, 50),
                'status' => 'active'
            ];
        }

        // Generate dummy harvest data
        $harvests = [];
        for ($i = 1; $i <= rand(3, 8); $i++) {
            $harvests[] = [
                'id' => $i,
                'harvest_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'crop_name' => $crops[array_rand($crops)]['name'],
                'quantity' => rand(50, 500),
                'unit' => 'kg',
                'unit_of_measure' => 'kg',
                'quality' => ['Good', 'Fair', 'Excellent'][array_rand(['Good', 'Fair', 'Excellent'])],
                'block_code' => 'BLK00' . rand(1, 3)
            ];
        }

        // Generate dummy marketing data
        $marketing = [];
        for ($i = 1; $i <= rand(2, 6); $i++) {
            $marketing[] = [
                'id' => $i,
                'market_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'crop_name' => $crops[array_rand($crops)]['name'],
                'buyer_name' => ['Local Market', 'Export Company', 'Processing Plant'][array_rand(['Local Market', 'Export Company', 'Processing Plant'])],
                'quantity' => rand(50, 300),
                'unit_of_measure' => 'kg',
                'market_price_per_unit' => rand(2, 10),
                'total_revenue' => 0
            ];
            $marketing[$i-1]['total_revenue'] = $marketing[$i-1]['quantity'] * $marketing[$i-1]['market_price_per_unit'];
        }

        // Generate dummy fertilizer data
        $fertilizers = [];
        for ($i = 1; $i <= rand(1, 4); $i++) {
            $fertilizers[] = [
                'id' => $i,
                'action_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'block_code' => 'BLK00' . rand(1, 3),
                'crop_name' => $crops[array_rand($crops)]['name'],
                'fertilizer_name' => ['NPK 15-15-15', 'Urea', 'Compost'][array_rand(['NPK 15-15-15', 'Urea', 'Compost'])],
                'quantity' => rand(10, 50),
                'unit' => 'kg',
                'unit_of_measure' => 'kg'
            ];
        }

        // Generate dummy pesticide data
        $pesticides = [];
        for ($i = 1; $i <= rand(1, 3); $i++) {
            $pesticides[] = [
                'id' => $i,
                'action_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'block_code' => 'BLK00' . rand(1, 3),
                'crop_name' => $crops[array_rand($crops)]['name'],
                'pesticide_name' => ['Insecticide A', 'Fungicide B', 'Herbicide C'][array_rand(['Insecticide A', 'Fungicide B', 'Herbicide C'])],
                'quantity' => rand(1, 10),
                'unit' => 'liters',
                'unit_of_measure' => 'liters'
            ];
        }

        // Generate dummy livestock data
        $livestock = [];
        for ($i = 1; $i <= rand(0, 3); $i++) {
            $livestock[] = [
                'id' => $i,
                'action_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'block_code' => 'LBK00' . rand(1, 2),
                'livestock_name' => ['Pigs', 'Chickens', 'Goats'][array_rand(['Pigs', 'Chickens', 'Goats'])],
                'he_total' => rand(5, 25),
                'she_total' => rand(5, 25),
                'livestock_color_code' => '#' . substr(md5('livestock'), 0, 6)
            ];
        }

        // Generate dummy disease data
        $diseases = [];
        for ($i = 1; $i <= rand(0, 2); $i++) {
            $diseases[] = [
                'id' => $i,
                'action_date' => date('Y-m-d', strtotime('-' . rand(1, 365) . ' days')),
                'block_code' => 'BLK00' . rand(1, 3),
                'crop_name' => $crops[array_rand($crops)]['name'],
                'infection_name' => ['Blight', 'Rust', 'Mosaic'][array_rand(['Blight', 'Rust', 'Mosaic'])],
                'disease_name' => ['Blight', 'Rust', 'Mosaic'][array_rand(['Blight', 'Rust', 'Mosaic'])],
                'infection_color_code' => '#' . substr(md5('disease'), 0, 6),
                'number_of_plants' => rand(10, 100),
                'hectares' => rand(1, 3)
            ];
        }

        // Calculate summaries
        $summaries = [
            'total_blocks' => count($farmBlocks),
            'total_livestock_blocks' => count($livestockBlocks),
            'total_harvests' => count($harvests),
            'total_marketing' => count($marketing),
            'total_fertilizer_applications' => count($fertilizers),
            'total_pesticide_applications' => count($pesticides),
            'total_livestock' => array_sum(array_map(function($l) {
                return $l['he_total'] + $l['she_total'];
            }, $livestock)),
            'total_disease_cases' => count($diseases),
            'total_revenue' => array_sum(array_column($marketing, 'total_revenue'))
        ];

        // Prepare data for view
        $data = [
            'title' => 'Farmer Profile',
            'page_header' => 'Farmer Profile',
            'farmer' => $farmer,
            'children' => $children,
            'farm_blocks' => $farmBlocks,
            'livestock_blocks' => $livestockBlocks,
            'harvests' => $harvests,
            'marketing' => $marketing,
            'fertilizers' => $fertilizers,
            'pesticides' => $pesticides,
            'livestock' => $livestock,
            'diseases' => $diseases,
            'summaries' => $summaries
        ];

        return view('staff_reports/staff_reports_farmers_profile', $data);
    }

    public function block_profile($block_id = null)
    {
        if (!$block_id) {
            return redirect()->to('/staff/reports/blocks');
        }

        // Get basic block information
        $block = $this->farmBlockModel
            ->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_crops.crop_color_code,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_blocks.id', $block_id)
            ->first();

        if (!$block) {
            return redirect()->to('/staff/reports/blocks');
        }

        // Get crops data
        $crops_data = $this->farmCropsDataModel
            ->select('
                crops_farm_crops_data.*,
                crops_farm_blocks.block_code,
                adx_crops.crop_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_crops_data.crop_id')
            ->where('crops_farm_crops_data.block_id', $block_id)
            ->where('crops_farm_crops_data.status', 'active')
            ->orderBy('crops_farm_crops_data.action_date', 'DESC')
            ->findAll();

        // Get diseases data
        $diseases_data = $this->farmDiseaseDataModel
            ->select('
                crops_farm_disease_data.*,
                adx_infections.name as infection_name,
                adx_infections.color_code as infection_color_code
            ')
            ->join('adx_infections', 'adx_infections.name = crops_farm_disease_data.disease_name', 'left')
            ->where('crops_farm_disease_data.block_id', $block_id)
            ->where('crops_farm_disease_data.status', 'active')
            ->orderBy('crops_farm_disease_data.action_date', 'DESC')
            ->findAll();

        // Get fertilizer data
        $fertilizer_data = $this->farmFertilizerDataModel
            ->select('crops_farm_fertilizer_data.*, adx_fertilizers.name as fertilizer_name')
            ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
            ->where('block_id', $block_id)
            ->where('crops_farm_fertilizer_data.status', 'active')
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get pesticides data
        $pesticides_data = $this->farmPesticidesDataModel
            ->select('crops_farm_pesticides_data.*, adx_pesticides.name as pesticide_name')
            ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
            ->where('block_id', $block_id)
            ->where('crops_farm_pesticides_data.status', 'active')
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get harvest data
        $harvest_data = $this->farmHarvestDataModel
            ->where('block_id', $block_id)
            ->where('status', 'active')
            ->orderBy('harvest_date', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Farm Block Profile - ' . $block['block_code'],
            'page_header' => 'Farm Block Profile',
            'block' => $block,
            'crops_data' => $crops_data,
            'diseases_data' => $diseases_data,
            'fertilizer_data' => $fertilizer_data,
            'pesticides_data' => $pesticides_data,
            'harvest_data' => $harvest_data
        ];

        return view('staff_reports/staff_reports_blocks_profile', $data);
    }

    public function crop_buyers()
    {
        // Get buyer summary data with transaction information
        $buyer_summary = $this->farmMarketingDataModel->getMarketSummaryByBuyer();

        // Get basic buyer information
        $all_buyers = $this->cropBuyersModel
            ->select('crop_buyers.*, adx_crops.crop_name, adx_province.name as province_name')
            ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
            ->join('adx_province', 'adx_province.id = crop_buyers.location_id AND crop_buyers.operation_span = "local"', 'left')
            ->where('crop_buyers.status', 'active')
            ->orderBy('crop_buyers.name', 'ASC')
            ->findAll();

        // Merge buyer data with transaction summary
        $buyers_data = [];
        foreach ($all_buyers as $buyer) {
            $buyer_stats = null;
            foreach ($buyer_summary as $summary) {
                if ($summary['buyer_id'] == $buyer['id']) {
                    $buyer_stats = $summary;
                    break;
                }
            }

            $buyers_data[] = [
                'id' => $buyer['id'],
                'buyer_code' => $buyer['buyer_code'],
                'name' => $buyer['name'],
                'crop_name' => $buyer['crop_name'],
                'contact_number' => $buyer['contact_number'],
                'email' => $buyer['email'],
                'operation_span' => $buyer['operation_span'],
                'location_name' => $buyer['operation_span'] === 'local' ? $buyer['province_name'] : 'Papua New Guinea',
                'address' => $buyer['address'],
                'status' => $buyer['status'],
                'created_at' => $buyer['created_at'],
                // Transaction data
                'transaction_count' => $buyer_stats['transaction_count'] ?? 0,
                'total_quantity' => $buyer_stats['total_quantity'] ?? 0,
                'total_value' => $buyer_stats['total_value'] ?? 0,
                'avg_price' => $buyer_stats['avg_price'] ?? 0,
                'total_freight_cost' => $buyer_stats['total_freight_cost'] ?? 0,
                'selling_locations' => $buyer_stats['selling_locations'] ?? '',
                'unit_of_measure' => $buyer_stats['unit_of_measure'] ?? ''
            ];
        }

        // Calculate summary statistics
        $stats = [
            'total_buyers' => count($all_buyers),
            'active_buyers' => count(array_filter($buyers_data, fn($b) => $b['transaction_count'] > 0)),
            'total_transactions' => array_sum(array_column($buyer_summary, 'transaction_count')),
            'total_value' => array_sum(array_column($buyer_summary, 'total_value')),
            'avg_transaction_value' => 0
        ];

        if ($stats['total_transactions'] > 0) {
            $stats['avg_transaction_value'] = $stats['total_value'] / $stats['total_transactions'];
        }

        $data = [
            'title' => 'Crop Buyers Report',
            'page_header' => 'Crop Buyers Report',
            'buyers_data' => $buyers_data,
            'stats' => $stats
        ];

        return view('staff_reports/crop_buyers_report', $data);
    }

    // Helper method to export reports to Excel/PDF if needed
    protected function exportReport($data, $type)
    {
        // Implementation for exporting reports
    }
}
