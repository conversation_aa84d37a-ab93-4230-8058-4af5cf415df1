<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Dummy Model that doesn't connect to database
 * Used for demo purposes with dummy data
 */
class DummyModel extends Model
{
    protected $table = 'dummy';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [];
    protected $useTimestamps = false;
    protected $validationRules = [];
    protected $validationMessages = [];
    protected $skipValidation = false;

    public function __construct($db = null, $validation = null)
    {
        // Don't call parent constructor to avoid database connection
        // Just initialize basic properties
        $this->tempReturnType = $this->returnType;
        $this->tempUseSoftDeletes = $this->useSoftDeletes;
        $this->tempAllowedFields = $this->allowedFields;
    }

    // Override methods to return dummy data or empty results
    public function findAll(int $limit = 0, int $offset = 0)
    {
        return [];
    }

    public function find($id = null)
    {
        return null;
    }

    public function first()
    {
        return null;
    }

    public function insert($data = null, bool $returnID = true)
    {
        return true;
    }

    public function update($id = null, $data = null): bool
    {
        return true;
    }

    public function delete($id = null, bool $purge = false)
    {
        return true;
    }

    public function where($key, $value = null, bool $escape = null)
    {
        return $this;
    }

    public function select($select = '*', ?bool $escape = null)
    {
        return $this;
    }

    public function join(string $table, string $cond, string $type = '', ?bool $escape = null)
    {
        return $this;
    }

    public function orderBy(string $orderBy, string $direction = '', ?bool $escape = null)
    {
        return $this;
    }

    public function limit(?int $value = null, ?int $offset = 0)
    {
        return $this;
    }
}
